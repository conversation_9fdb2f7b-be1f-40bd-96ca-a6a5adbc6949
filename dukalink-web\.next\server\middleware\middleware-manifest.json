{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|_next/data|favicon.ico|images|fonts).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|_next/data|favicon.ico|images|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+oNudljpj9p7dWGY3XiSV8GquDtokFGZN6RtJ8/C9UU=", "__NEXT_PREVIEW_MODE_ID": "21ce02fd3b5b913d9cc8f73ae45ec5f2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "033adee7b3e3596c20405974bb84f73210cc416426a6346173740d6a51fe6aed", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b23abe2adc36f3f53445b91365206802237947613f095fc9c99c9df6aec08a2d"}}}, "instrumentation": null, "functions": {}}