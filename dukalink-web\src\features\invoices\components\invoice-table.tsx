"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Invoice, InvoiceStatus } from "../types";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { InvoiceStatusBadge } from "./invoice-status-badge";
import { CustomPagination } from "@/components/ui/custom-pagination";
import {
  Search,
  Plus,
  ArrowUpDown,
  Eye,
  FileText,
  ChevronUp,
  ChevronDown,
  Filter,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDeleteInvoice, useGenerateInvoicePdf } from "../hooks/use-invoices";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface InvoiceTableProps {
  invoices: Invoice[];
  isLoading: boolean;
  pagination: {
    page: number;
    totalPages: number;
    totalItems: number;
    limit?: number;
  };
  onPageChange: (page: number) => void;
  onSearch: (query: string) => void;
  onSort?: (field: string, direction: 'asc' | 'desc') => void;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
  onItemsPerPageChange?: (value: number) => void;
  activeFilters?: {
    status?: InvoiceStatus;
    type?: string;
    customer_id?: number;
    supplier_id?: number;
    date_range?: { start: string; end: string };
  };
  onClearFilters?: () => void;
}

export function InvoiceTable({
  invoices,
  isLoading,
  pagination,
  onPageChange,
  onSearch,
  onSort,
  sortField = 'created_at',
  sortDirection = 'desc',
  onItemsPerPageChange,
  activeFilters,
  onClearFilters,
}: InvoiceTableProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const deleteInvoice = useDeleteInvoice();
  const generatePdf = useGenerateInvoicePdf();
  const [invoiceToDelete, setInvoiceToDelete] = useState<number | null>(null);

  const handleSearch = () => {
    onSearch(searchQuery);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleViewInvoice = (id: number) => {
    router.push(`/invoices/${id}`);
  };

  const handleGeneratePdf = async (id: number) => {
    try {
      const blob = await generatePdf.mutateAsync(id);
      const url = URL.createObjectURL(blob);
      window.open(url, '_blank');
    } catch (error) {
      console.error("Error generating PDF:", error);
    }
  };

  const handleSort = (field: string) => {
    if (!onSort) return;

    const newDirection = field === sortField && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(field, newDirection);
  };

  // Helper to render sort indicators
  const renderSortIndicator = (field: string) => {
    if (field !== sortField) {
      return <ArrowUpDown className="ml-2 h-4 w-4 opacity-50" />;
    }

    return sortDirection === 'asc'
      ? <ChevronUp className="ml-2 h-4 w-4" />
      : <ChevronDown className="ml-2 h-4 w-4" />;
  };

  // Count active filters
  const getActiveFilterCount = () => {
    if (!activeFilters) return 0;

    let count = 0;
    if (activeFilters.status) count++;
    if (activeFilters.type) count++;
    if (activeFilters.customer_id) count++;
    if (activeFilters.supplier_id) count++;
    if (activeFilters.date_range) count++;

    return count;
  };

  return (
    <div className="space-y-4">
      {/* Search and actions */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex w-full sm:w-auto gap-2">
          <Input
            placeholder="Search invoices..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full sm:w-[300px]"
          />
          <Button variant="outline" onClick={handleSearch}>
            <Search className="h-4 w-4" />
          </Button>

          {/* Active filters indicator */}
          {getActiveFilterCount() > 0 && (
            <Button
              variant="outline"
              onClick={onClearFilters}
              className="gap-2"
            >
              <Filter className="h-4 w-4" />
              <Badge variant="secondary" className="rounded-full px-2 py-0 text-xs">
                {getActiveFilterCount()}
              </Badge>
              Clear
            </Button>
          )}
        </div>
        {/* <Button onClick={() => router.push("/invoices/new")}>
          <Plus className="mr-2 h-4 w-4" /> New Invoice
        </Button> */}
      </div>

      {/* Invoice table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-16">#</TableHead>
              <TableHead>Invoice ID</TableHead>
              <TableHead
                className={cn("cursor-pointer", sortField === 'invoice_number' && "text-primary")}
                onClick={() => handleSort('invoice_number')}
              >
                <div className="flex items-center">
                  Invoice #
                  {renderSortIndicator('invoice_number')}
                </div>
              </TableHead>
              <TableHead
                className={cn("cursor-pointer", sortField === 'invoice_date' && "text-primary")}
                onClick={() => handleSort('invoice_date')}
              >
                <div className="flex items-center">
                  Date
                  {renderSortIndicator('invoice_date')}
                </div>
              </TableHead>
              <TableHead
                className={cn("cursor-pointer", sortField === 'due_date' && "text-primary")}
                onClick={() => handleSort('due_date')}
              >
                <div className="flex items-center">
                  Due Date
                  {renderSortIndicator('due_date')}
                </div>
              </TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead
                className={cn("cursor-pointer", sortField === 'total_amount' && "text-primary")}
                onClick={() => handleSort('total_amount')}
              >
                <div className="flex items-center">
                  Amount
                  {renderSortIndicator('total_amount')}
                </div>
              </TableHead>
              <TableHead
                className={cn("cursor-pointer", sortField === 'status' && "text-primary")}
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  Status
                  {renderSortIndicator('status')}
                </div>
              </TableHead>
              <TableHead>KRA Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Show loading skeletons when loading
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Skeleton className="h-5 w-8" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-32" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-16" />
                  </TableCell>
                  <TableCell className="text-right">
                    <Skeleton className="h-9 w-20 ml-auto" />
                  </TableCell>
                </TableRow>
              ))
            ) : invoices.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8">
                  No invoices found. Create your first invoice.
                </TableCell>
              </TableRow>
            ) : (
              invoices.map((invoice, index) => {
                const rowIndex = (pagination.page - 1) * (pagination.limit || 50) + index + 1;
                return (
                  <TableRow key={invoice.id}>
                    <TableCell className="text-center text-sm text-muted-foreground">
                      {rowIndex}
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {invoice.id}
                    </TableCell>
                    <TableCell className="font-medium">
                      {invoice.invoice_number}
                    </TableCell>
                    <TableCell>{formatDate(invoice.invoice_date)}</TableCell>
                    <TableCell>{formatDate(invoice.due_date)}</TableCell>
                    <TableCell>
                      {invoice.supplier?.name || "N/A"}
                    </TableCell>
                    <TableCell>{formatCurrency(invoice.total_amount)}</TableCell>
                    <TableCell>
                      <InvoiceStatusBadge status={invoice.status} />
                    </TableCell>
                    <TableCell>
                      {invoice.kra_integrated ? (
                        <div className="flex items-center text-green-600">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <span className="text-xs">Integrated</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-gray-400">
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                          <span className="text-xs">Not Integrated</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <span className="sr-only">Open menu</span>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="lucide lucide-more-vertical"
                            >
                              <circle cx="12" cy="12" r="1" />
                              <circle cx="12" cy="5" r="1" />
                              <circle cx="12" cy="19" r="1" />
                            </svg>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewInvoice(invoice.id)}>
                            <Eye className="mr-2 h-4 w-4" /> View
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleGeneratePdf(invoice.id)}>
                            <FileText className="mr-2 h-4 w-4" /> Generate PDF
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {!isLoading && invoices.length > 0 && (
        <CustomPagination
          currentPage={pagination.page}
          totalPages={pagination.totalPages}
          onPageChange={onPageChange}
          totalItems={pagination.totalItems}
          itemsPerPage={pagination.limit || 50}
          onItemsPerPageChange={onItemsPerPageChange}
        />
      )}
    </div>
  );
}
